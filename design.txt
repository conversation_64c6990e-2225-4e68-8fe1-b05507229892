You are given a task to integrate an existing React component in the codebase

The codebase should support:
- React with TypeScript
- Tailwind CSS
- Modern build tools (Vite/Next.js)

If your project doesn't support these, provide instructions on how to set them up.

Copy-paste this component to your project:
App.tsx
```tsx
"use client"

import React, { useState, useRef, useEffect } from 'react'
import { motion, useInView, useAnimation } from 'framer-motion'
import { 
  Code, 
  Smartphone, 
  Zap, 
  Globe, 
  Search,
  Palette,
  Users,
  FileText,
  BarChart3,
  TrendingUp,
  Target,
  MousePointer
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface ServiceFeature {
  icon: React.ReactNode
  title: string
  description: string
}

interface Service {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  features: ServiceFeature[]
  image: React.ReactNode
}

const FloatingIcon = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => {
  return (
    <motion.div
      initial={{ y: 0 }}
      animate={{ y: [-10, 10, -10] }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
        delay
      }}
      className="inline-block"
    >
      {children}
    </motion.div>
  )
}

const CodeAnimation = () => {
  const [currentLine, setCurrentLine] = useState(0)
  const [isTyping, setIsTyping] = useState(false)
  const codeLines = [
    "const website = {",
    "  design: 'modern',",
    "  responsive: true,",
    "  performance: 'optimized',",
    "  framework: 'React'",
    "}"
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setIsTyping(true)
      setTimeout(() => {
        setCurrentLine((prev) => (prev + 1) % codeLines.length)
        setIsTyping(false)
      }, 300)
    }, 1200)
    return () => clearInterval(interval)
  }, [])

  return (
    <motion.div 
      className="bg-gradient-to-br from-black to-gray-800 text-white p-8 rounded-2xl font-mono text-sm border border-white/10 shadow-2xl overflow-hidden relative"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      <div className="absolute top-4 left-4 flex gap-2">
        <div className="w-3 h-3 rounded-full bg-red-500"></div>
        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
        <div className="w-3 h-3 rounded-full bg-green-500"></div>
      </div>
      <div className="mt-8">
        {codeLines.map((line, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0.3, x: -10 }}
            animate={{ 
              opacity: index <= currentLine ? 1 : 0.3,
              x: index <= currentLine ? 0 : -10,
              color: index === currentLine ? '#ffffff' : '#888888'
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="leading-relaxed flex items-center"
          >
            <span className="text-gray-500 mr-4 text-xs">{String(index + 1).padStart(2, '0')}</span>
            {line}
            {index === currentLine && isTyping && (
              <motion.span
                animate={{ opacity: [1, 0] }}
                transition={{ duration: 0.5, repeat: Infinity }}
                className="ml-1 text-white"
              >
                |
              </motion.span>
            )}
          </motion.div>
        ))}
      </div>
      <motion.div
        className="absolute -right-4 -bottom-4 w-16 h-16 bg-white/5 rounded-full"
        animate={{ scale: [1, 1.2, 1], rotate: [0, 180, 360] }}
        transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
      />
    </motion.div>
  )
}

const DesignAnimation = () => {
  const [activeElement, setActiveElement] = useState(0)
  const [designPhase, setDesignPhase] = useState(0)
  const elements = ['header', 'content', 'sidebar', 'footer']
  const phases = ['wireframe', 'design', 'prototype', 'final']

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveElement((prev) => (prev + 1) % elements.length)
      setDesignPhase((prev) => (prev + 1) % phases.length)
    }, 1800)
    return () => clearInterval(interval)
  }, [])

  return (
    <motion.div 
      className="bg-gradient-to-br from-white to-gray-50 border-2 border-black p-8 rounded-2xl shadow-2xl relative overflow-hidden"
      whileHover={{ scale: 1.02, rotateY: 5 }}
      transition={{ duration: 0.3 }}
    >
      <div className="absolute top-4 right-4 text-xs font-mono text-gray-400 uppercase tracking-widest">
        {phases[designPhase]}
      </div>
      
      <div className="space-y-4">
        <motion.div
          className={`h-12 rounded-lg relative overflow-hidden ${
            activeElement === 0 ? 'bg-black' : 'bg-gray-200'
          }`}
          animate={{ 
            scale: activeElement === 0 ? 1.03 : 1,
            backgroundColor: activeElement === 0 ? '#000000' : '#e5e7eb'
          }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
        >
          {activeElement === 0 && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: [-100, 300] }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />
          )}
        </motion.div>
        
        <div className="flex gap-4">
          <motion.div
            className={`h-32 flex-1 rounded-lg relative overflow-hidden ${
              activeElement === 1 ? 'bg-black' : 'bg-gray-200'
            }`}
            animate={{ 
              scale: activeElement === 1 ? 1.03 : 1,
              backgroundColor: activeElement === 1 ? '#000000' : '#e5e7eb'
            }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
          >
            {activeElement === 1 && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                animate={{ x: [-100, 400] }}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
            )}
          </motion.div>
          <motion.div
            className={`h-32 w-24 rounded-lg relative overflow-hidden ${
              activeElement === 2 ? 'bg-black' : 'bg-gray-200'
            }`}
            animate={{ 
              scale: activeElement === 2 ? 1.03 : 1,
              backgroundColor: activeElement === 2 ? '#000000' : '#e5e7eb'
            }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
          >
            {activeElement === 2 && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                animate={{ x: [-50, 150] }}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
            )}
          </motion.div>
        </div>
        
        <motion.div
          className={`h-8 rounded-lg relative overflow-hidden ${
            activeElement === 3 ? 'bg-black' : 'bg-gray-200'
          }`}
          animate={{ 
            scale: activeElement === 3 ? 1.03 : 1,
            backgroundColor: activeElement === 3 ? '#000000' : '#e5e7eb'
          }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
        >
          {activeElement === 3 && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: [-100, 300] }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />
          )}
        </motion.div>
      </div>
      
      <div className="absolute bottom-4 left-4 flex gap-1">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-2 h-2 rounded-full ${
              i === activeElement ? 'bg-black' : 'bg-gray-300'
            }`}
            animate={{ scale: i === activeElement ? 1.2 : 1 }}
            transition={{ duration: 0.3 }}
          />
        ))}
      </div>

      <motion.div
        className="absolute -top-2 -right-2 w-12 h-12 bg-black/5 rounded-full"
        animate={{ rotate: [0, 360] }}
        transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
      />
    </motion.div>
  )
}

const StrategyAnimation = () => {
  const [progress, setProgress] = useState(0)
  const [metrics, setMetrics] = useState([45, 78, 92])
  const [activeMetric, setActiveMetric] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = (prev + 8) % 101
        if (newProgress < prev) {
          setMetrics([
            Math.floor(Math.random() * 40) + 60,
            Math.floor(Math.random() * 40) + 60,
            Math.floor(Math.random() * 40) + 60
          ])
        }
        return newProgress
      })
      setActiveMetric((prev) => (prev + 1) % 3)
    }, 250)
    return () => clearInterval(interval)
  }, [])

  return (
    <motion.div 
      className="bg-gradient-to-br from-white via-gray-50 to-white border-2 border-black p-8 rounded-2xl shadow-2xl relative overflow-hidden"
      whileHover={{ scale: 1.02, rotateX: 2 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <motion.span 
            className="text-lg font-bold text-black tracking-wide"
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Growth Analytics
          </motion.span>
          <motion.span 
            className="text-2xl font-mono text-black"
            key={progress}
            initial={{ scale: 1.2, opacity: 0.7 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {progress}%
          </motion.span>
        </div>
        
        <div className="relative">
          <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
            <motion.div
              className="bg-black h-full rounded-full relative"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                animate={{ x: [-50, 200] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              />
            </motion.div>
          </div>
          <motion.div
            className="absolute -top-1 bg-black w-2 h-6 rounded-full"
            style={{ left: `${progress}%` }}
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 0.5, repeat: Infinity }}
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          {metrics.map((metric, i) => (
            <motion.div
              key={i}
              className="relative"
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                className="h-16 bg-gray-200 rounded-lg overflow-hidden relative"
                animate={{ 
                  backgroundColor: activeMetric === i ? '#000000' : '#e5e7eb',
                  scale: activeMetric === i ? 1.03 : 1
                }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
              >
                <motion.div
                  className="absolute bottom-0 left-0 right-0 bg-black"
                  animate={{ height: `${metric}%` }}
                  transition={{ duration: 0.6, ease: "easeOut" }}
                />
                {activeMetric === i && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-t from-transparent via-white/20 to-transparent"
                    animate={{ y: [-20, 80] }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  />
                )}
              </motion.div>
              <motion.div
                className="text-center mt-2 text-xs font-mono text-gray-600"
                animate={{ opacity: activeMetric === i ? 1 : 0.6 }}
              >
                {metric}%
              </motion.div>
            </motion.div>
          ))}
        </div>

        <div className="flex justify-center gap-2">
          {['SEO', 'Speed', 'UX'].map((label, i) => (
            <motion.div
              key={label}
              className={`px-3 py-1 rounded-full text-xs font-medium border ${
                activeMetric === i 
                  ? 'bg-black text-white border-black' 
                  : 'bg-white text-black border-gray-300'
              }`}
              animate={{ 
                scale: activeMetric === i ? 1.05 : 1,
                backgroundColor: activeMetric === i ? '#000000' : '#ffffff'
              }}
              transition={{ duration: 0.3 }}
            >
              {label}
            </motion.div>
          ))}
        </div>
      </div>

      <motion.div
        className="absolute top-4 right-4 w-8 h-8 border-2 border-black/10 rounded-full"
        animate={{ rotate: [0, 360] }}
        transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
      >
        <motion.div
          className="w-2 h-2 bg-black rounded-full mt-1 ml-1"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      </motion.div>
    </motion.div>
  )
}

const ServiceCard = ({ service, index }: { service: Service; index: number }) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const controls = useAnimation()
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    if (isInView) {
      controls.start("visible")
    }
  }, [isInView, controls])

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 60, rotateX: -15 },
        visible: { 
          opacity: 1, 
          y: 0,
          rotateX: 0,
          transition: { 
            duration: 0.8,
            delay: index * 0.2,
            ease: [0.25, 0.46, 0.45, 0.94]
          }
        }
      }}
      className="group perspective-1000"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <motion.div
        whileHover={{ 
          scale: 1.02,
          rotateY: 2,
          rotateX: 2,
          z: 50
        }}
        transition={{ duration: 0.4, ease: "easeOut" }}
      >
        <Card className="h-full bg-gradient-to-br from-white via-gray-50 to-white border-2 border-black hover:shadow-[12px_12px_0px_0px_#000] transition-all duration-500 overflow-hidden relative">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-black/5 to-transparent"
            animate={isHovered ? { x: [-100, 400] } : {}}
            transition={{ duration: 1.5, ease: "easeInOut" }}
          />
          
          <CardContent className="p-10 relative z-10">
            <div className="flex flex-col lg:flex-row gap-10 items-center">
              <div className="flex-1 space-y-8">
                <motion.div 
                  className="flex items-center gap-6"
                  initial={{ opacity: 0, x: -30 }}
                  animate={isInView ? { opacity: 1, x: 0 } : {}}
                  transition={{ delay: (index * 0.2) + 0.3, duration: 0.6 }}
                >
                  <motion.div
                    whileHover={{ 
                      scale: 1.15, 
                      rotate: [0, -10, 10, 0],
                      backgroundColor: "#000000"
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="p-4 bg-black text-white rounded-2xl shadow-lg cursor-pointer"
                    transition={{ duration: 0.3 }}
                  >
                    <FloatingIcon delay={index * 0.5}>
                      {service.icon}
                    </FloatingIcon>
                  </motion.div>
                  <div>
                    <motion.h3 
                      className="text-3xl font-bold text-black mb-3 tracking-tight"
                      initial={{ opacity: 0, y: 20 }}
                      animate={isInView ? { opacity: 1, y: 0 } : {}}
                      transition={{ delay: (index * 0.2) + 0.4, duration: 0.5 }}
                    >
                      {service.title}
                    </motion.h3>
                    <motion.p 
                      className="text-gray-600 text-lg leading-relaxed"
                      initial={{ opacity: 0, y: 15 }}
                      animate={isInView ? { opacity: 1, y: 0 } : {}}
                      transition={{ delay: (index * 0.2) + 0.5, duration: 0.5 }}
                    >
                      {service.description}
                    </motion.p>
                  </div>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {service.features.map((feature, featureIndex) => (
                    <motion.div
                      key={featureIndex}
                      initial={{ opacity: 0, x: -30, scale: 0.9 }}
                      animate={isInView ? { opacity: 1, x: 0, scale: 1 } : {}}
                      transition={{ 
                        delay: (index * 0.2) + (featureIndex * 0.15) + 0.6,
                        duration: 0.5,
                        ease: "easeOut"
                      }}
                      whileHover={{ 
                        scale: 1.03,
                        backgroundColor: "#f9fafb",
                        transition: { duration: 0.2 }
                      }}
                      className="flex items-start gap-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 cursor-pointer border border-transparent hover:border-gray-200"
                    >
                      <motion.div
                        whileHover={{ 
                          scale: 1.3, 
                          rotate: 360,
                          transition: { duration: 0.5 }
                        }}
                        className="mt-1 text-black flex-shrink-0"
                      >
                        {feature.icon}
                      </motion.div>
                      <div>
                        <h4 className="font-bold text-black mb-2 text-lg">{feature.title}</h4>
                        <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ delay: (index * 0.2) + 1.0, duration: 0.5 }}
                  className="flex gap-4"
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Badge 
                      variant="outline" 
                      className="border-2 border-black text-black hover:bg-black hover:text-white transition-all duration-300 px-6 py-2 text-sm font-semibold cursor-pointer"
                    >
                      Learn More
                    </Badge>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Badge 
                      className="bg-black text-white hover:bg-gray-800 transition-all duration-300 px-6 py-2 text-sm font-semibold cursor-pointer"
                    >
                      Get Started
                    </Badge>
                  </motion.div>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8, rotateY: -20 }}
                animate={isInView ? { opacity: 1, scale: 1, rotateY: 0 } : {}}
                transition={{ 
                  delay: (index * 0.2) + 0.4,
                  duration: 0.8,
                  ease: "easeOut"
                }}
                whileHover={{ 
                  scale: 1.05,
                  rotateY: 5,
                  rotateX: -5,
                  transition: { duration: 0.3 }
                }}
                className="flex-shrink-0 w-full lg:w-96"
              >
                {service.image}
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}

const OurServicesSection = () => {
  const services: Service[] = [
    {
      id: 'web-development',
      title: 'Web Development',
      description: 'Custom websites and web applications built with modern technologies',
      icon: <Code size={24} />,
      features: [
        {
          icon: <Smartphone size={16} />,
          title: 'Responsive Design',
          description: 'Websites that work perfectly on all devices and screen sizes'
        },
        {
          icon: <Zap size={16} />,
          title: 'Performance Optimization',
          description: 'Lightning-fast loading times and smooth user experiences'
        },
        {
          icon: <Search size={16} />,
          title: 'SEO Friendly',
          description: 'Built with search engine optimization best practices'
        },
        {
          icon: <Globe size={16} />,
          title: 'Cross-browser Compatibility',
          description: 'Consistent experience across all major web browsers'
        }
      ],
      image: <CodeAnimation />
    },
    {
      id: 'ui-ux-design',
      title: 'UI/UX Design',
      description: 'Beautiful and intuitive user interfaces that convert visitors into customers',
      icon: <Palette size={24} />,
      features: [
        {
          icon: <Users size={16} />,
          title: 'User Research',
          description: 'Deep understanding of your target audience and their needs'
        },
        {
          icon: <FileText size={16} />,
          title: 'Wireframing & Prototyping',
          description: 'Detailed blueprints and interactive prototypes before development'
        },
        {
          icon: <Palette size={16} />,
          title: 'Visual Design',
          description: 'Stunning visual designs that reflect your brand identity'
        },
        {
          icon: <MousePointer size={16} />,
          title: 'Usability Testing',
          description: 'Rigorous testing to ensure optimal user experience'
        }
      ],
      image: <DesignAnimation />
    },
    {
      id: 'digital-strategy',
      title: 'Digital Strategy',
      description: 'Comprehensive digital solutions to grow your online presence',
      icon: <TrendingUp size={24} />,
      features: [
        {
          icon: <Target size={16} />,
          title: 'Digital Marketing',
          description: 'Strategic campaigns to reach and engage your target audience'
        },
        {
          icon: <FileText size={16} />,
          title: 'Content Strategy',
          description: 'Compelling content that drives engagement and conversions'
        },
        {
          icon: <BarChart3 size={16} />,
          title: 'Analytics & Reporting',
          description: 'Data-driven insights to measure and improve performance'
        },
        {
          icon: <TrendingUp size={16} />,
          title: 'Conversion Optimization',
          description: 'Systematic improvements to maximize your conversion rates'
        }
      ],
      image: <StrategyAnimation />
    }
  ]

  const headerRef = useRef(null)
  const isHeaderInView = useInView(headerRef, { once: true })

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4 max-w-7xl">
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: 30 }}
          animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center mb-16"
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-foreground mb-6"
          >
            Our Services
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            We offer comprehensive web development services to help your business succeed online
          </motion.p>
        </motion.div>

        <div className="space-y-12">
          {services.map((service, index) => (
            <ServiceCard key={service.id} service={service} index={index} />
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <motion.p 
            className="text-xl text-muted-foreground mb-10 leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.6 }}
            viewport={{ once: true }}
          >
            Ready to transform your digital presence?
          </motion.p>
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.6 }}
            viewport={{ once: true }}
          >
            <motion.button
              whileHover={{ 
                scale: 1.05,
                boxShadow: "8px_8px_0px_0px_#666"
              }}
              whileTap={{ scale: 0.95 }}
              className="bg-black text-white px-10 py-5 rounded-2xl font-bold text-lg hover:shadow-[4px_4px_0px_0px_#666] transition-all duration-300 border-2 border-black"
            >
              Get Started Today
            </motion.button>
            <motion.button
              whileHover={{ 
                scale: 1.05,
                backgroundColor: "#000000",
                color: "#ffffff"
              }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-black px-10 py-5 rounded-2xl font-bold text-lg border-2 border-black hover:shadow-[4px_4px_0px_0px_#666] transition-all duration-300"
            >
              View Portfolio
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default OurServicesSection

```

Install NPM dependencies:
```bash
npm install framer-motion @remixicon/react
```


Additional setup:
1. Make sure you have Tailwind CSS configured in your project
2. Update your main App component or create a new component file
3. Import and use the component in your application

The component is designed to work standalone and includes all necessary styling and functionality.