"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from './components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './components/ui/card'
import { Badge } from './components/ui/badge'
import { HeroBackground } from './components/ui/hero-background'
import { Component as EtheralShadow } from './components/ui/etheral-shadow'
import OurServicesSection from './components/OurServicesSection'
import { ChevronRight, Code, Palette, Smartphone, Globe, Users, Target, Award, ArrowRight, Sparkles, Zap, Star } from 'lucide-react'

interface TypingEffectProps {
  texts: string[]
  className?: string
  typingSpeed?: number
  deletingSpeed?: number
  pauseDuration?: number
}

const TypingEffect: React.FC<TypingEffectProps> = ({
  texts,
  className = "",
  typingSpeed = 100,
  deletingSpeed = 50,
  pauseDuration = 2000
}) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [currentText, setCurrentText] = useState("")
  const [isDeleting, setIsDeleting] = useState(false)
  const [isPaused, setIsPaused] = useState(false)

  useEffect(() => {
    const currentFullText = texts[currentTextIndex]

    const timeout = setTimeout(() => {
      if (isPaused) {
        setIsPaused(false)
        setIsDeleting(true)
        return
      }

      if (isDeleting) {
        // Deleting characters
        if (currentText.length > 0) {
          setCurrentText(currentFullText.substring(0, currentText.length - 1))
        } else {
          // Finished deleting, move to next text
          setIsDeleting(false)
          setCurrentTextIndex((prevIndex) => (prevIndex + 1) % texts.length)
        }
      } else {
        // Typing characters
        if (currentText.length < currentFullText.length) {
          setCurrentText(currentFullText.substring(0, currentText.length + 1))
        } else {
          // Finished typing, pause before deleting
          setIsPaused(true)
        }
      }
    }, isPaused ? pauseDuration : isDeleting ? deletingSpeed : typingSpeed)

    return () => clearTimeout(timeout)
  }, [currentText, currentTextIndex, isDeleting, isPaused, texts, typingSpeed, deletingSpeed, pauseDuration])

  return (
    <span className={`inline-block ${className}`}>
      {currentText}
      <span className="animate-pulse">|</span>
    </span>
  )
}



interface ProcessStepProps {
  step: number
  title: string
  description: string
  isLast?: boolean
}

const ProcessStep: React.FC<ProcessStepProps> = ({ step, title, description, isLast = false }) => {
  return (
    <div className="flex items-start space-x-4">
      <div className="flex flex-col items-center">
        <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
          {step}
        </div>
        {!isLast && <div className="w-px h-16 bg-border mt-4" />}
      </div>
      <div className="flex-1 pb-8">
        <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </div>
  )
}

const LandingPage: React.FC = () => {
  const switchingTexts = [
    "Modern Websites",
    "Web Applications", 
    "E-commerce Stores",
    "Digital Solutions"
  ]



  const processSteps = [
    {
      title: "Discovery & Planning",
      description: "We start by understanding your business goals, target audience, and project requirements through detailed consultation and research."
    },
    {
      title: "Design & Prototyping",
      description: "Our design team creates wireframes and prototypes, ensuring the user experience is intuitive and aligns with your brand identity."
    },
    {
      title: "Development & Testing",
      description: "We build your solution using cutting-edge technologies, followed by rigorous testing to ensure quality and performance."
    },
    {
      title: "Launch & Support",
      description: "After successful deployment, we provide ongoing support and maintenance to keep your digital solution running smoothly."
    }
  ]

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden bg-black text-white">
        {/* Etheral Shadow Background */}
        <div className="absolute inset-0 z-0">
          <EtheralShadow
            color="rgba(75, 85, 99, 0.8)"
            animation={{ scale: 80, speed: 70 }}
            noise={{ opacity: 0.3, scale: 1.0 }}
            sizing="fill"
            className="w-full h-full"
          />
        </div>

        {/* New Animated Background */}
        <HeroBackground gradient={true} blur={true} />
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center">
            <Badge className="mb-6 animate-fade-in-down bg-white text-black hover:bg-gray-200 transition-all duration-300">
              <Sparkles className="w-4 h-4 mr-2" />
              Web Development Agency
            </Badge>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight mb-6 animate-fade-in-up text-white">
              We Create{" "}
              <TypingEffect
                texts={switchingTexts}
                className="text-white relative inline-block"
                typingSpeed={150}
                deletingSpeed={75}
                pauseDuration={1500}
              />
              <br />
              <span className="relative">
                That Drive Results
                <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-white to-white/50 animate-pulse"></div>
              </span>
            </h1>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              Transform your digital presence with our expert web development services.
              We build fast, secure, and scalable solutions that help your business grow.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <Button size="lg" className="text-lg group hover:scale-105 transition-all duration-300 hover:shadow-lg bg-white text-black hover:bg-gray-200">
                Start Your Project
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </Button>
              <Button variant="outline" size="lg" className="text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg group border-white text-white hover:bg-white hover:text-black">
                <Star className="mr-2 w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                View Our Work
              </Button>
            </div>

            {/* Floating icons */}
            <div className="mt-16 relative">
              <div className="flex justify-center space-x-8 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                <div className="p-3 bg-white/5 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110 cursor-pointer">
                  <Code className="w-6 h-6 text-white" />
                </div>
                <div className="p-3 bg-white/5 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110 cursor-pointer" style={{ animationDelay: '0.1s' }}>
                  <Palette className="w-6 h-6 text-white" />
                </div>
                <div className="p-3 bg-white/5 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110 cursor-pointer" style={{ animationDelay: '0.2s' }}>
                  <Smartphone className="w-6 h-6 text-white" />
                </div>
                <div className="p-3 bg-white/5 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110 cursor-pointer" style={{ animationDelay: '0.3s' }}>
                  <Zap className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <OurServicesSection />

      {/* Process Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">Our Process</h2>
            <p className="text-xl text-muted-foreground">
              A proven methodology that ensures successful project delivery
            </p>
          </div>
          <div className="space-y-8">
            {processSteps.map((step, index) => (
              <ProcessStep
                key={index}
                step={index + 1}
                title={step.title}
                description={step.description}
                isLast={index === processSteps.length - 1}
              />
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl sm:text-4xl font-bold mb-6">About Our Agency</h2>
              <p className="text-lg text-muted-foreground mb-6">
                We are a team of passionate web developers and designers dedicated to creating 
                exceptional digital experiences. With over 5 years of experience in the industry, 
                we have helped hundreds of businesses establish their online presence and achieve 
                their digital goals.
              </p>
              <p className="text-lg text-muted-foreground mb-8">
                Our expertise spans across modern web technologies, ensuring that every project 
                we deliver is built with the latest standards and best practices. We believe in 
                transparent communication, agile development, and delivering results that exceed expectations.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">500+</div>
                  <div className="text-muted-foreground">Projects Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">98%</div>
                  <div className="text-muted-foreground">Client Satisfaction</div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <Card className="bg-background border-border">
                <CardHeader>
                  <Users className="w-8 h-8 text-primary mb-2" />
                  <CardTitle>Expert Team</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Our skilled developers and designers work collaboratively to deliver outstanding results.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background border-border">
                <CardHeader>
                  <Target className="w-8 h-8 text-primary mb-2" />
                  <CardTitle>Goal-Oriented</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    We focus on achieving your business objectives through strategic digital solutions.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background border-border">
                <CardHeader>
                  <Award className="w-8 h-8 text-primary mb-2" />
                  <CardTitle>Quality Assured</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Every project undergoes rigorous testing to ensure the highest quality standards.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background border-border">
                <CardHeader>
                  <Globe className="w-8 h-8 text-primary mb-2" />
                  <CardTitle>Global Reach</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    We serve clients worldwide, bringing local expertise with global perspectives.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact-section" className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Let's discuss how we can help bring your digital vision to life
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="bg-background border-border">
              <CardHeader>
                <CardTitle className="text-2xl">Get In Touch</CardTitle>
                <p className="text-muted-foreground">
                  Fill out the form below and we'll get back to you within 24 hours.
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="John"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Doe"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Service Interested In
                  </label>
                  <select className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">Select a service</option>
                    <option value="web-development">Web Development</option>
                    <option value="ui-ux-design">UI/UX Design</option>
                    <option value="digital-strategy">Digital Strategy</option>
                    <option value="consultation">Free Consultation</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Project Budget
                  </label>
                  <select className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">Select budget range</option>
                    <option value="under-5k">Under $5,000</option>
                    <option value="5k-10k">$5,000 - $10,000</option>
                    <option value="10k-25k">$10,000 - $25,000</option>
                    <option value="25k-plus">$25,000+</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Project Description
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                    placeholder="Tell us about your project..."
                  />
                </div>
                <Button className="w-full" size="lg">
                  Send Message
                  <ChevronRight className="ml-2 w-5 h-5" />
                </Button>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <div className="space-y-8">
              <Card className="bg-background border-border">
                <CardHeader>
                  <CardTitle className="text-xl">Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Globe className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground">Email</p>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Smartphone className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground">Phone</p>
                      <p className="text-muted-foreground">+****************</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Target className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground">Location</p>
                      <p className="text-muted-foreground">San Francisco, CA</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-background border-border">
                <CardHeader>
                  <CardTitle className="text-xl">Why Choose Us?</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Star className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-foreground">Expert Team</p>
                      <p className="text-sm text-muted-foreground">5+ years of experience in web development</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Zap className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-foreground">Fast Delivery</p>
                      <p className="text-sm text-muted-foreground">Quick turnaround without compromising quality</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Award className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-foreground">Quality Guaranteed</p>
                      <p className="text-sm text-muted-foreground">100% satisfaction guarantee on all projects</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

function App() {
  return (
    <LandingPage />
  )
}

export default App
